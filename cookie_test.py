#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie测试工具 - 独立的Cookie验证脚本
用于快速诊断Cookie认证问题
"""

import requests
import json
import sys
from typing import <PERSON>ple, Any

def test_cookie_connection(cookie: str, base_url: str = "https://app.augmentcode.com/api") -> Tuple[bool, str, Any]:
    """
    测试Cookie连接
    
    Args:
        cookie: Cookie字符串
        base_url: API基础URL
        
    Returns:
        (成功状态, 错误信息, 响应数据)
    """
    
    print("🔍 开始Cookie连接测试")
    print("=" * 50)
    
    # 检查Cookie基本格式
    if not cookie:
        return False, "Cookie为空", None
    
    print(f"📍 API URL: {base_url}/team")
    print(f"🍪 Cookie长度: {len(cookie)} 字符")
    
    # 检查关键字段
    required_fields = ['_session=', 'ajs_user_id=']
    missing_fields = []
    
    for field in required_fields:
        if field not in cookie:
            missing_fields.append(field.replace('=', ''))
    
    if missing_fields:
        return False, f"Cookie缺少关键字段: {', '.join(missing_fields)}", None
    
    print("✅ Cookie格式检查通过")
    
    # 构建请求头
    headers = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "cache-control": "no-cache",
        "pragma": "no-cache",
        "priority": "u=1, i",
        "sec-ch-ua": '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "cookie": cookie,
        "Referer": "https://app.augmentcode.com/account/subscription",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }
    
    try:
        print("🚀 发送API请求...")
        
        # 发送请求
        response = requests.get(f"{base_url}/team", headers=headers, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ 成功获取JSON数据")
                
                # 分析数据结构
                if isinstance(data, dict):
                    print(f"📊 数据结构分析:")
                    for key, value in data.items():
                        if isinstance(value, list):
                            print(f"  - {key}: {len(value)} 项")
                        elif isinstance(value, dict):
                            print(f"  - {key}: 对象 ({len(value)} 个字段)")
                        else:
                            print(f"  - {key}: {type(value).__name__}")
                
                return True, "连接成功", data
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"📄 响应内容前500字符: {response.text[:500]}")
                return False, f"JSON解析失败: {e}", None
                
        else:
            error_msg = f"HTTP {response.status_code}"
            print(f"❌ 请求失败: {error_msg}")
            print(f"📄 响应内容: {response.text[:500]}")
            
            # 分析具体错误
            if response.status_code == 401:
                return False, "认证失败 - Cookie可能已过期或无效", None
            elif response.status_code == 403:
                return False, "访问被拒绝 - 账号可能没有权限", None
            elif response.status_code == 404:
                return False, "API端点不存在 - 请检查URL", None
            else:
                return False, f"HTTP错误 {response.status_code}: {response.text[:200]}", None
                
    except requests.exceptions.Timeout:
        return False, "连接超时 - 请检查网络连接", None
    except requests.exceptions.ConnectionError:
        return False, "连接错误 - 无法连接到服务器", None
    except Exception as e:
        return False, f"未知错误: {str(e)}", None

def main():
    """主函数"""
    print("🍪 Cookie连接测试工具")
    print("=" * 50)
    
    # 获取Cookie输入
    if len(sys.argv) > 1:
        cookie = sys.argv[1]
    else:
        print("请输入Cookie字符串:")
        cookie = input().strip()
    
    if not cookie:
        print("❌ 未提供Cookie")
        return
    
    # 执行测试
    success, message, data = test_cookie_connection(cookie)
    
    print("\n" + "=" * 50)
    print("🔧 测试结果")
    print("=" * 50)
    
    if success:
        print("✅ Cookie测试成功!")
        print(f"📊 消息: {message}")
        
        if data and isinstance(data, dict):
            # 显示团队信息摘要
            if 'members' in data:
                print(f"👥 团队成员: {len(data['members'])} 人")
            if 'pendingInvitations' in data:
                print(f"📧 待处理邀请: {len(data['pendingInvitations'])} 个")
            if 'teamName' in data:
                print(f"🏢 团队名称: {data['teamName']}")
        
        print("\n💡 建议: Cookie有效，可以在团队管理工具中使用")
        
    else:
        print("❌ Cookie测试失败!")
        print(f"📊 错误: {message}")
        
        # 提供解决建议
        print("\n💡 解决建议:")
        if "过期" in message or "401" in message:
            print("1. 重新登录 https://app.augmentcode.com")
            print("2. 从浏览器开发者工具重新复制Cookie")
            print("3. 确保复制完整的Cookie字符串")
        elif "权限" in message or "403" in message:
            print("1. 确认账号有团队管理权限")
            print("2. 联系团队管理员获取权限")
        elif "超时" in message:
            print("1. 检查网络连接")
            print("2. 尝试使用VPN")
        else:
            print("1. 检查Cookie格式是否正确")
            print("2. 确认API URL是否正确")
            print("3. 联系技术支持")

if __name__ == "__main__":
    main()
