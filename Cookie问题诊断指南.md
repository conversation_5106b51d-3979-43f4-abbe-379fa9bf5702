# Cookie问题诊断指南

## 🔍 问题概述

如果您在使用团队管理工具时遇到"无法获取team信息"的错误，通常是Cookie认证问题导致的。本指南将帮助您快速诊断和解决这个问题。

## 🍪 什么是Cookie？

Cookie是浏览器存储的认证信息，包含了您的登录状态和权限信息。团队管理工具需要这些信息来访问API。

## 📋 快速诊断步骤

### 1. 使用内置测试工具

1. 打开团队管理工具
2. 点击菜单栏的"工具" → "配置设置"
3. 在"API配置"标签页中：
   - 检查Cookie输入框是否有内容
   - 点击"验证Cookie"按钮检查格式
   - 点击"测试连接"按钮验证连接

### 2. 使用独立测试脚本

运行提供的`cookie_test.py`脚本：

```bash
python cookie_test.py
```

然后粘贴您的Cookie进行测试。

### 3. 手动检查Cookie

检查Cookie中是否包含以下关键字段：
- `_session=` - 会话认证令牌（最重要）
- `ajs_user_id=` - 用户ID
- `_ga=` - Google Analytics
- `ph_phc` - PostHog分析数据

## 🔧 如何获取正确的Cookie

### 方法1：从浏览器开发者工具获取

1. 打开浏览器，访问 https://app.augmentcode.com
2. 登录您的账号
3. 按F12打开开发者工具
4. 切换到"Network"(网络)标签页
5. 刷新页面或进行任何操作
6. 找到任意一个请求，点击查看
7. 在请求头中找到"Cookie"字段
8. 复制完整的Cookie值

### 方法2：从浏览器地址栏获取

1. 在已登录的页面按F12
2. 在Console标签页输入：`document.cookie`
3. 复制返回的完整字符串

## ❌ 常见错误及解决方案

### 错误1：401 Unauthorized（认证失败）

**原因：** Cookie已过期或无效

**解决方案：**
1. 重新登录网站
2. 获取新的Cookie
3. 在配置中更新Cookie

### 错误2：403 Forbidden（权限不足）

**原因：** 账号没有团队管理权限

**解决方案：**
1. 确认您的账号有团队管理权限
2. 联系团队管理员获取权限
3. 使用有权限的账号重新获取Cookie

### 错误3：404 Not Found（API不存在）

**原因：** API URL配置错误

**解决方案：**
1. 检查API URL是否为：`https://app.augmentcode.com/api`
2. 确认网络可以访问该地址

### 错误4：Timeout（连接超时）

**原因：** 网络连接问题

**解决方案：**
1. 检查网络连接
2. 尝试使用VPN
3. 检查防火墙设置

### 错误5：JSON解析错误

**原因：** 服务器返回了HTML错误页面而不是JSON

**解决方案：**
1. 通常是Cookie问题，重新获取Cookie
2. 检查是否被重定向到登录页面

## 🔍 调试技巧

### 1. 查看详细日志

在团队管理工具中：
1. 查看"系统日志"面板
2. 注意红色的错误信息
3. 查看具体的错误代码和描述

### 2. 使用浏览器测试

在浏览器中直接访问API：
1. 打开 https://app.augmentcode.com/api/team
2. 如果返回JSON数据，说明Cookie有效
3. 如果跳转到登录页面，说明Cookie无效

### 3. 检查Cookie完整性

确保Cookie：
- 没有被截断
- 包含所有分号分隔的字段
- 没有多余的空格或换行符

## 💡 最佳实践

1. **定期更新Cookie**：Cookie有时效性，建议定期更新
2. **保护Cookie安全**：Cookie包含敏感信息，不要泄露给他人
3. **使用测试功能**：在使用前先测试连接
4. **备份配置**：保存有效的配置以备后用

## 🆘 仍然无法解决？

如果按照以上步骤仍然无法解决问题，请：

1. 运行`cookie_test.py`脚本并保存输出结果
2. 截图错误信息
3. 记录具体的错误代码
4. 联系技术支持并提供以上信息

## 📞 技术支持

如需进一步帮助，请提供以下信息：
- 错误截图
- Cookie测试脚本的输出
- 使用的浏览器和版本
- 网络环境信息

---

*最后更新：2025年1月*
